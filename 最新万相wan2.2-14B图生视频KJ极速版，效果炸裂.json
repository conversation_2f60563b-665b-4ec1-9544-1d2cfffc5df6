{"id": "c6e410bc-5e2c-460b-ae81-c91b6094fbb1", "revision": 0, "last_node_id": 153, "last_link_id": 236, "nodes": [{"id": 132, "type": "JWInteger", "pos": [2135.300048828125, -1607.7171630859375], "size": [315, 58], "flags": {"pinned": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [210]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [832], "color": "#233", "bgcolor": "#355"}, {"id": 133, "type": "JWInteger", "pos": [2140.865478515625, -1452.7166748046875], "size": [315, 58], "flags": {"pinned": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [207]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [81], "color": "#233", "bgcolor": "#355"}, {"id": 126, "type": "VHS_VideoCombine", "pos": [3226.291259765625, -1885.6534423828125], "size": [698.6392211914062, 1254.23095703125], "flags": {"pinned": true}, "order": 33, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 230}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideo2_2_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideo2_2_I2V_00002_xxpqf_1754225340.mp4", "workflow": "WanVideo2_2_I2V_00002.png", "fullpath": "/data/ComfyUI/personal/97e72773b3062cc68e83823cfbb22717/output/WanVideo2_2_I2V_00002.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}, "color": "#323", "bgcolor": "#535"}, {"id": 122, "type": "GetImageSizeAndCount", "pos": [2198.716552734375, -938.2296752929688], "size": [240.41265869140625, 86], "flags": {"pinned": true}, "order": 31, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 201}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [229]}, {"label": "width", "name": "640 width", "type": "INT"}, {"label": "height", "name": "832 height", "type": "INT"}, {"label": "count", "name": "81 count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSizeAndCount", "cnr_id": "comfyui-kjnodes", "ver": "a6b867b63a29ca48ddb15c589e17a9f2d8530d57", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 148, "type": "LayerUtility: PurgeVRAM", "pos": [1806.7857666015625, -1493.413330078125], "size": [315, 82], "flags": {"pinned": true}, "order": 30, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 234}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 147, "type": "LayerUtility: PurgeVRAM", "pos": [1811.5377197265625, -1324.835205078125], "size": [315, 82], "flags": {"pinned": true}, "order": 28, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 233}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 120, "type": "WanVideoDecode", "pos": [1812.4320068359375, -1157.9864501953125], "size": [315, 198], "flags": {"pinned": true}, "order": 29, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 197}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 198}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [201]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#223", "bgcolor": "#335"}, {"id": 143, "type": "ImageFromBatch+", "pos": [2159.3447265625, -1129.367919921875], "size": [315, 82], "flags": {"pinned": true}, "order": 32, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 229}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [230]}], "properties": {"Node name for S&R": "ImageFromBatch+", "cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [3, -1], "color": "#223", "bgcolor": "#335"}, {"id": 128, "type": "WanVideoImageToVideoEncode", "pos": [1490.702880859375, -1411.986328125], "size": [308.2320251464844, 390], "flags": {"pinned": true}, "order": 23, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 203}, {"label": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS"}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 204}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK"}, {"label": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT"}, {"label": "add_cond_latents", "name": "add_cond_latents", "shape": 7, "type": "ADD_COND_LATENTS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 205}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 206}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 207}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [225, 226]}], "properties": {"Node name for S&R": "WanVideoImageToVideoEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 81, 0, 1, 1, true, false, false], "color": "#223", "bgcolor": "#335"}, {"id": 119, "type": "WanVideoVAELoader", "pos": [1496.061767578125, -957.4940185546875], "size": [315, 82], "flags": {"pinned": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [197, 203]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#223", "bgcolor": "#335"}, {"id": 116, "type": "WanVideoSetBlockSwap", "pos": [189.24581909179688, -1836.4078369140625], "size": [201.76815795898438, 46], "flags": {"pinned": true}, "order": 20, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 191}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 192}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "links": [199]}], "properties": {"Node name for S&R": "WanVideoSetBlockSwap", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 121, "type": "WanVideoSetLoRAs", "pos": [173.97032165527344, -1560.445068359375], "size": [222.27981567382812, 46], "flags": {"pinned": true}, "order": 24, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 199}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 200}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "links": [211]}], "properties": {"Node name for S&R": "WanVideoSetLoRAs", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 118, "type": "WanVideoSetLoRAs", "pos": [528.297119140625, -1339.818359375], "size": [222.27981567382812, 46], "flags": {"pinned": true}, "order": 25, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 195}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 196}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "links": [215]}], "properties": {"Node name for S&R": "WanVideoSetLoRAs", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 129, "type": "WanVideoTextEncode", "pos": [181.2021942138672, -1219.7587890625], "size": [474.3573303222656, 316.48370361328125], "flags": {"pinned": true}, "order": 22, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "shape": 7, "type": "WANTEXTENCODER", "link": 208}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL"}, {"label": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 224}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [227, 228]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["美丽的女人做了个胜利的手势", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true, false, "cpu"], "color": "#223", "bgcolor": "#335"}, {"id": 130, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1522.212890625, -1884.9058837890625], "size": [504, 330], "flags": {"pinned": true}, "order": 18, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 209}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 210}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [204]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT", "links": [205]}, {"label": "height", "name": "height", "type": "INT", "links": [206]}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "crop", "lanc<PERSON>s", "16", "longest", 720, "#000000"], "color": "#323", "bgcolor": "#535"}, {"id": 145, "type": "PrimitiveNode", "pos": [2195.515869140625, -1318.4757080078125], "size": [210, 82], "flags": {"pinned": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "seed"}, "links": [231, 232]}], "properties": {"Run widget replace on values": false}, "widgets_values": [104464882174676, "randomize"], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "CreateCFGScheduleFloatList", "pos": [467.69427490234375, -1580.07275390625], "size": [298.3199157714844, 178], "flags": {"pinned": true}, "order": 19, "mode": 0, "inputs": [{"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 219}], "outputs": [{"label": "float_list", "name": "float_list", "type": "FLOAT", "links": [213]}], "properties": {"Node name for S&R": "CreateCFGScheduleFloatList", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f", "widget_ue_connectable": {}}, "widgets_values": [30, 2, 2, "linear", 0, 0.01], "color": "#223", "bgcolor": "#335"}, {"id": 117, "type": "WanVideoSetBlockSwap", "pos": [182.17747497558594, -1695.0113525390625], "size": [201.76815795898438, 46], "flags": {"pinned": true}, "order": 21, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 193}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 194}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "links": [195]}], "properties": {"Node name for S&R": "WanVideoSetBlockSwap", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 131, "type": "LoadImage", "pos": [2530.285400390625, -1889.529296875], "size": [658.9733276367188, 996.1378784179688], "flags": {"pinned": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [209]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["d93ebe61984a6c12378e28062c0e629b437f91f28bca3d9d9a634d68f36da1a1.webp", "image", ""], "color": "#223", "bgcolor": "#335"}, {"id": 115, "type": "WanVideoModelLoader", "pos": [421.9718933105469, -838.6139526367188], "size": [477.4410095214844, 274], "flags": {"pinned": true}, "order": 5, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [193]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_2-I2V-A14B-LOW_fp8_e4m3fn_scaled_KJ.safetensors", "bf16", "fp8_e4m3fn_scaled", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 123, "type": "WanVideoLoraSelect", "pos": [443.4539794921875, -443.0337219238281], "size": [472.9589538574219, 150], "flags": {"pinned": true}, "order": 6, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [196]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1, false, false], "color": "#223", "bgcolor": "#335"}, {"id": 125, "type": "LoadWanVideoT5TextEncoder", "pos": [967.5795288085938, -758.1303100585938], "size": [377.1661376953125, 130], "flags": {"pinned": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [208]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 114, "type": "WanVideoModelLoader", "pos": [-115.07312774658203, -827.1358032226562], "size": [477.4410095214844, 274], "flags": {"pinned": true}, "order": 8, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [191]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_2-I2V-A14B-HIGH_fp8_e4m3fn_scaled_KJ.safetensors", "bf16", "fp8_e4m3fn_scaled", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "WanVideoLoraSelect", "pos": [-106.92996215820312, -460.25689697265625], "size": [464.8004455566406, 150], "flags": {"pinned": true}, "order": 9, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [200]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 3.0000000000000004, false, false], "color": "#223", "bgcolor": "#335"}, {"id": 149, "type": "<PERSON>downNote", "pos": [1728.142822265625, -770.39501953125], "size": [398.31475830078125, 499.16046142578125], "flags": {"pinned": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "title": "我的主页", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["```\n|欢迎进入我的主页\nQQ交流群：386692323\n```\n---\n\n### 公众号直达：[wangyi AI Studio](https://mp.weixin.qq.com/s/PRX5-5s07xXAqrxrMfpTQw )👈\n### QQ交流群扫码加入：[386692323](https://mmbiz.qpic.cn/sz_mmbiz_png/2L985ceJXgdaTS7dhS1rLrB7LrPEZQLd8u4ED41iaoFdjopj5XlSicAmX52ThlXln9hgCbWWuKcwzA15P65p0wDw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)👈\n### B站直达：[wangyi-AI-Studio](https://space.bilibili.com/2022161153?spm_id_from=333.1387.0.0 )👈\n### 淘宝店铺直达：[wangyi-AI-Studio](https://shop125324266.taobao.com/ )👈\n### runninghub主页：[wangyi-AI-Studio](https://www.runninghub.cn/user-center/1894041412860805122?utm_source=kol01-RH116 )👈\n### LIBLIBAI主页：[wangyi-AI-Studio](https://www.liblib.art/userpage/54a3c4ccfea84ffd821678b6e54a7b88/publish/workflow )👈\n### wangyi AI-ComfyUI超级整合包：[wangyi-AI-Studio](https://mp.weixin.qq.com/s/se5HzO_1UTeTiFQiRFe-sA )👈\n### AI翻唱1对1教学：[wangyi-AI-Studio](https://mp.weixin.qq.com/s/pTbxj-FrBQ1xATjyoN4aYQ )👈\n### AI实时变声精调：[wangyi-AI-Studio](https://mp.weixin.qq.com/s/Om42tYTnvRNaUQXFj1DGpg )👈\n```\n购买过我ComfyUI超级整合包的，可以加入高端微信粉丝群，适合AI小白、进阶、帮带、报错解决等学习者。 \n\n|关注公众号可了解|\n|:---:\n|> 1、最新AI动态；|\n|> 2、最新工作流分享；|\n|> 3、常用节点搭建；|\n|> 4、工作流报错解决；|\n|> 5、最新节点讲解；| \n|> 6、最新整合包，解压即用；|\n|...|\n\n💬+V：yuan2xq1313 \n（购买过我超级整合包的，可以加我私信，请注明来意）\n"], "color": "#a9bafe", "bgcolor": "#95a6ea"}, {"id": 137, "type": "INTConstant", "pos": [553.09130859375, -1862.80810546875], "size": [210, 58], "flags": {"pinned": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [212, 217, 219]}], "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "a6b867b63a29ca48ddb15c589e17a9f2d8530d57", "widget_ue_connectable": {}}, "widgets_values": [10], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 138, "type": "INTConstant", "pos": [554.5921020507812, -1736.9779052734375], "size": [210, 58], "flags": {"pinned": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [214, 218]}], "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "a6b867b63a29ca48ddb15c589e17a9f2d8530d57", "widget_ue_connectable": {}}, "widgets_values": [5], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 127, "type": "WanVideoBlockSwap", "pos": [124.53325653076172, -1449.01806640625], "size": [315, 154], "flags": {"pinned": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [192, 194]}], "properties": {"Node name for S&R": "WanVideoBlockSwap", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [39, false, false, false, 1], "color": "#323", "bgcolor": "#535"}, {"id": 150, "type": "Note", "pos": [-109.26136779785156, -1421.6370849609375], "size": [210, 88.09832763671875], "flags": {"pinned": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["如果是32G以上显存的，右边的39可以调到20，以获得更快的生成速度！"], "color": "#432", "bgcolor": "#653"}, {"id": 151, "type": "WanVideoEasyCache", "pos": [905.9481201171875, -1823.9686279296875], "size": [315, 130], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [235]}], "properties": {"Node name for S&R": "WanVideoEasyCache", "widget_ue_connectable": {}}, "widgets_values": [0.015, 3, -1, "offload_device"]}, {"id": 152, "type": "WanVideoEasyCache", "pos": [1246.8162841796875, -1829.4490966796875], "size": [315, 130], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [236]}], "properties": {"Node name for S&R": "WanVideoEasyCache", "widget_ue_connectable": {}}, "widgets_values": [0.015, 3, -1, "offload_device"]}, {"id": 135, "type": "WanVideoSampler", "pos": [1158.759521484375, -1877.6790771484375], "size": [320.79998779296875, 975], "flags": {"pinned": true}, "order": 27, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 215}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 228}, {"label": "image_embeds", "name": "image_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": 226}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": 216}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 236}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 217}, {"label": "start_step", "name": "start_step", "shape": 7, "type": "INT", "widget": {"name": "start_step"}, "link": 218}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 232}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [198, 234]}, {"label": "denoised_samples", "name": "denoised_samples", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [6, 1, 8, 104464882174676, "fixed", true, "unipc", 0, 1, false, "comfy", 10, -1, false], "color": "#223", "bgcolor": "#335"}, {"id": 134, "type": "WanVideoSampler", "pos": [810.696044921875, -1876.9703369140625], "size": [320.79998779296875, 975], "flags": {"pinned": true}, "order": 26, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 211}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 227}, {"label": "image_embeds", "name": "image_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": 225}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 235}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 212}, {"label": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": 213}, {"label": "end_step", "name": "end_step", "shape": 7, "type": "INT", "widget": {"name": "end_step"}, "link": 214}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 231}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [216, 233]}, {"label": "denoised_samples", "name": "denoised_samples", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": [6, 1, 8, 104464882174676, "fixed", true, "unipc", 0, 1, false, "comfy", 0, 10, false], "color": "#223", "bgcolor": "#335"}, {"id": 141, "type": "CR Prompt Text", "pos": [2086.69384765625, -1877.4202880859375], "size": [400, 200], "flags": {"pinned": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "prompt", "name": "prompt", "type": "STRING", "links": [224]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Prompt Text", "widget_ue_connectable": {}}, "widgets_values": ["美丽的女人从树上下来，面对镜头撩撩裙子"], "color": "#233", "bgcolor": "#355"}], "links": [[191, 114, 0, 116, 0, "WANVIDEOMODEL"], [192, 127, 0, 116, 1, "BLOCKSWAPARGS"], [193, 115, 0, 117, 0, "WANVIDEOMODEL"], [194, 127, 0, 117, 1, "BLOCKSWAPARGS"], [195, 117, 0, 118, 0, "WANVIDEOMODEL"], [196, 123, 0, 118, 1, "WANVIDLORA"], [197, 119, 0, 120, 0, "WANVAE"], [198, 135, 0, 120, 1, "LATENT"], [199, 116, 0, 121, 0, "WANVIDEOMODEL"], [200, 124, 0, 121, 1, "WANVIDLORA"], [201, 120, 0, 122, 0, "IMAGE"], [203, 119, 0, 128, 0, "WANVAE"], [204, 130, 0, 128, 2, "IMAGE"], [205, 130, 3, 128, 8, "INT"], [206, 130, 4, 128, 9, "INT"], [207, 133, 0, 128, 10, "INT"], [208, 125, 0, 129, 0, "WANTEXTENCODER"], [209, 131, 0, 130, 0, "IMAGE"], [210, 132, 0, 130, 2, "INT"], [211, 121, 0, 134, 0, "WANVIDEOMODEL"], [212, 137, 0, 134, 17, "INT"], [213, 136, 0, 134, 18, "FLOAT"], [214, 138, 0, 134, 19, "INT"], [215, 118, 0, 135, 0, "WANVIDEOMODEL"], [216, 134, 0, 135, 3, "LATENT"], [217, 137, 0, 135, 17, "INT"], [218, 138, 0, 135, 18, "INT"], [219, 137, 0, 136, 0, "INT"], [224, 141, 0, 129, 2, "STRING"], [225, 128, 0, 134, 2, "WANVIDIMAGE_EMBEDS"], [226, 128, 0, 135, 2, "WANVIDIMAGE_EMBEDS"], [227, 129, 0, 134, 1, "WANVIDEOTEXTEMBEDS"], [228, 129, 0, 135, 1, "WANVIDEOTEXTEMBEDS"], [229, 122, 0, 143, 0, "IMAGE"], [230, 143, 0, 126, 0, "IMAGE"], [231, 145, 0, 134, 20, "INT"], [232, 145, 0, 135, 19, "INT"], [233, 134, 0, 147, 0, "*"], [234, 135, 0, 148, 0, "*"], [235, 151, 0, 134, 6, "CACHEARGS"], [236, 152, 0, 135, 6, "CACHEARGS"]], "groups": [{"id": 3, "title": "最新万相wan2.2-14B图生视频KJ极速版，效果炸裂--关注wangyi AI Studio公众号", "bounding": [-155.22422790527344, -2146.700927734375, 4101.77099609375, 1960.7232666015625], "color": "#88A", "font_size": 100, "flags": {"pinned": true}}], "config": {}, "extra": {"ds": {"scale": 0.9646149645000139, "offset": [-2088.735845842748, 1596.7031194484994]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "frontendVersion": "1.24.1", "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "comfy-core": "0.3.26"}}, "version": 0.4}